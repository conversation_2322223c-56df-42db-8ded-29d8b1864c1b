<div id="toast-container" class="fixed top-4 right-4 z-50 space-y-4 w-full max-w-sm">

    @foreach (['success' => '✓', 'error' => '❌', 'info' => 'ℹ️'] as $type => $icon)
        @if (session($type))
            @php
                $borderColor = [
                    'success' => 'border-blue-500',
                    'error' => 'border-red-500',
                    'info' => 'border-sky-500',
                ][$type];

                $bgColor = [
                    'success' => 'bg-blue-500',
                    'error' => 'bg-red-500',
                    'info' => '',
                ][$type];
            @endphp

            <div
                class="toast opacity-0 translate-x-6 transition duration-500 ease-in-out flex items-start p-4 text-white bg-gray-800/80 backdrop-blur-md border border-white/10 rounded-xl shadow-xl relative {{ $borderColor }} border-l-4">

                <!-- Icon -->
                <div
                    class="flex items-center justify-center w-8 h-8 text-xl mr-3 {{ $bgColor }} rounded-sm shrink-0">
                    {{ $icon }}
                </div>

                <!-- Message -->
                <div class="flex-1 text-sm font-medium pt-1">
                    {{ session($type) }}
                </div>

                <!-- Close Button -->
                <button
                    onclick="
                        const toast = this.closest('.toast');
                        toast.classList.remove('opacity-100', 'translate-x-0');
                        toast.classList.add('opacity-0', 'translate-x-6');
                        setTimeout(() => toast.remove(), 300);
                    "
                    class="ml-4 mt-1 text-white/70 hover:text-white transition-all duration-200 flex items-start justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor" stroke-width="2">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        @endif
    @endforeach
</div>

<!-- 🔄 Show & Auto-hide Toasts AFTER Page Load -->
<script>
    document.addEventListener('DOMContentLoaded', () => {
        const toasts = document.querySelectorAll('.toast');

        toasts.forEach(el => {
            // Show with smooth animation
            requestAnimationFrame(() => {
                el.classList.remove('opacity-0', 'translate-x-6');
                el.classList.add('opacity-100', 'translate-x-0');
            });

            // Auto-hide after 4s
            setTimeout(() => {
                el.classList.remove('opacity-100', 'translate-x-0');
                el.classList.add('opacity-0', 'translate-x-6');
                setTimeout(() => el.remove(), 300);
            }, 4000);
        });
    });
</script>
