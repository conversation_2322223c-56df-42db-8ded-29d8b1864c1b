<?php

namespace App\Http\Controllers\Guest;

use App\Http\Controllers\Controller;
use App\Models\Plan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Exception;

class MainController extends Controller
{
    public function index()
    {
        return view('guest.index');
    }

    public function about()
    {
        return view('guest.about');
    }

    public function terms()
    {
        return view('guest.terms');
    }

    public function privacy()
    {
        return view('guest.privacy');
    }

    public function pricing()
    {
        $plans = Plan::with('services')
            ->where('status', 1)
            ->orderBy('sort_order', 'asc')
            ->orderBy('is_popular', 'desc')
            ->get();
        return view('guest.pricing', compact('plans'));
    }

    public function contact()
    {
        return view('guest.contact');
    }

    public function news()
    {
        return view('guest.news');
    }

    public function contactSubmit(Request $request)
    {
        try {
            $request->validate([
                'firstName' => 'required|string|max:255',
                'lastName' => 'required|string|max:255',
                'email' => 'required|email|max:255',
                'phone' => 'nullable|string|max:20',
                'subject' => 'required|string|max:255',
                'message' => 'required|string|max:2000',
            ]);

            // Log the contact form submission
            Log::info('Contact form submitted', [
                'name' => $request->firstName . ' ' . $request->lastName,
                'email' => $request->email,
                'subject' => $request->subject,
                'ip' => $request->ip(),
                'timestamp' => now()
            ]);

            // Here you can add email sending logic if needed
            // Mail::to('<EMAIL>')->send(new ContactFormMail($request->all()));

            return redirect()->route('contact')->with('success', 'Thank you for your message! We\'ll get back to you within 24 hours.');

        } catch (Exception $e) {
            Log::error('Contact form submission failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
                'ip' => $request->ip()
            ]);

            return redirect()->route('contact')->with('error', 'Sorry, there was an error sending your message. Please try again or call us directly.');
        }
    }

    public function bookingSubmit(Request $request)
    {
        try {
            $request->validate([
                'firstName' => 'required|string|max:255',
                'lastName' => 'required|string|max:255',
                'email' => 'required|email|max:255',
                'cellPhone' => 'required|string|max:20',
                'address' => 'required|string|max:500',
                'city' => 'required|string|max:255',
                'zipCode' => 'required|string|max:10',
                'serviceType' => 'required|string',
                'frequency' => 'required|string',
                'startDate' => 'required|date|after:today',
                'hasGateCode' => 'required|in:yes,no',
                'gateCode' => 'required_if:hasGateCode,yes|nullable|string|max:50',
                'specialInstructions' => 'nullable|string|max:1000',
            ]);

            // Log the booking form submission
            Log::info('Booking form submitted', [
                'name' => $request->firstName . ' ' . $request->lastName,
                'email' => $request->email,
                'address' => $request->address . ', ' . $request->city . ' ' . $request->zipCode,
                'service_type' => $request->serviceType,
                'frequency' => $request->frequency,
                'start_date' => $request->startDate,
                'ip' => $request->ip(),
                'timestamp' => now()
            ]);

            // Here you can add email sending logic or database storage
            // Mail::to('<EMAIL>')->send(new BookingFormMail($request->all()));

            return redirect()->route('home', ['#booking'])->with('success', 'Thank you for your booking request! We\'ll contact you within 24 hours to confirm your service and discuss payment options.');

        } catch (Exception $e) {
            Log::error('Booking form submission failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
                'ip' => $request->ip()
            ]);

            return redirect()->route('home', ['#booking'])->with('error', 'Sorry, there was an error processing your booking request. Please try again or call us directly at (405)634-6150.');
        }
    }


}
