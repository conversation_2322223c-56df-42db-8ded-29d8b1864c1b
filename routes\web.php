<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Artisan;
use App\Http\Controllers\Web\AuthController;
use App\Http\Controllers\Admin\HomeController;
use App\Http\Controllers\Guest\MainController;
use App\Http\Controllers\Admin\PlanController;


Route::get('/cache', function () {
    Artisan::call('optimize:clear');
    return redirect()->route('login')->with('success', 'All caches cleared successfully!');
});

Route::middleware(['redirect'])->group(function () {
    // Guest Routes
    Route::get('/', [MainController::class, 'index'])->name('home');
    Route::get('/about', [MainController::class, 'about'])->name('about');
    Route::get('/terms', [MainController::class, 'terms'])->name('terms');
    Route::get('/privacy', [MainController::class, 'privacy'])->name('privacy');
    Route::get('/pricing', [MainController::class, 'pricing'])->name('pricing');
    Route::get('/contact', [MainController::class, 'contact'])->name('contact');

    // Auth Routes
    Route::get('login', [AuthController::class, 'index'])->name('login');
    Route::get('register', [AuthController::class, 'register'])->name('register');
    Route::post('login-post', [AuthController::class, 'login'])->name('login.post');    
});

// Logout
Route::middleware('auth')->prefix('admin')->group(function () {
    Route::post('logout', [AuthController::class, 'logout'])->name('logout');

    Route::get('dashboard', [HomeController::class, 'index'])->name('dashboard');
    Route::get('profile', [HomeController::class, 'showProfile'])->name('profile.show');
    Route::put('profile-update', [HomeController::class, 'updateProfile'])->name('profile.update');

    Route::resource('plan', PlanController::class);
    Route::post('plan/{id}/status', [PlanController::class, 'status'])->name('plan.status');
    Route::post('plan/{id}/popular', [PlanController::class, 'popular'])->name('plan.popular');


});
